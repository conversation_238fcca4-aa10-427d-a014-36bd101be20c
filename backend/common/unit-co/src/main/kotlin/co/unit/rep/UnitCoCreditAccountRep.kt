package co.unit.rep

import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.rep.UpdaterRep
import co.highbeam.serialization.readValueNotNull
import co.highbeam.validation.RepValidation
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import com.fasterxml.jackson.databind.ser.std.StdSerializer
import java.util.UUID

object UnitCoCreditAccountRep {
  enum class Status { Open, Frozen, Closed; }

  enum class Type(val value: String) {
    CreditAccount("creditAccount");
  }

  sealed class FrozenReason {
    object Fraud : FrozenReason()
    data class Other(val reasonText: String) : FrozenReason()
  }

  @JsonSerialize(using = Creator.Serializer::class)
  data class Creator(
    val businessGuid: UUID,
    val customerId: String,
    val creditLimit: Money,
    val name: String,
    val creditTerms: String,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()

    internal class Serializer : StdSerializer<Creator>(Creator::class.java) {
      override fun serialize(value: Creator, gen: JsonGenerator, provider: SerializerProvider) =
        gen.writeObject(
          mapOf(
            "type" to Type.CreditAccount.value,
            "attributes" to mapOf(
              "creditTerms" to value.creditTerms,
              "creditLimit" to value.creditLimit,
              "idempotencyKey" to UUID.randomUUID(),
              "tags" to mapOf(
                "businessGuid" to value.businessGuid,
                "name" to value.name,
                "officialName" to "Highbeam Cards",
              ),
            ),
            "relationships" to mapOf(
              "customer" to mapOf("data" to mapOf("type" to "customer", "id" to value.customerId)),
            ),
          ),
        )
    }
  }

  @JsonSerialize(using = Updater.Serializer::class)
  data class Updater(
    val unitCoCreditAccountId: String,
    val creditLimit: Money,
  ) : UpdaterRep {
    override fun validate(): RepValidation = RepValidation.none()

    internal class Serializer : StdSerializer<Updater>(Updater::class.java) {
      override fun serialize(value: Updater, gen: JsonGenerator, provider: SerializerProvider) =
        gen.writeObject(
          mapOf(
            "type" to "creditAccount",
            "attributes" to mapOf(
              "creditLimit" to value.creditLimit,
            )
          ),
        )
    }
  }

  @JsonSerialize(using = Freeze.Serializer::class)
  data class Freeze(
    val unitCoCreditAccountId: String,
    val reason: FrozenReason,
  ) : UpdaterRep {
    override fun validate(): RepValidation = RepValidation.none()

    internal class Serializer : StdSerializer<Freeze>(Freeze::class.java) {
      override fun serialize(value: Freeze, gen: JsonGenerator, provider: SerializerProvider) {
        val reason = when (value.reason) {
          is FrozenReason.Fraud -> "Fraud"
          is FrozenReason.Other -> "Other"
        }
        val reasonText = when (value.reason) {
          is FrozenReason.Fraud -> null
          is FrozenReason.Other -> value.reason.reasonText
        }

        return gen.writeObject(
          mapOf(
            "type" to "creditAccountFreeze",
            "attributes" to buildMap {
              put("reason", reason)
              reasonText?.let { put("reasonText", it) }
            }
          )
        )
      }
    }
  }

  @JsonDeserialize(using = Complete.Deserializer::class)
  data class Complete(
    val id: String,
    val name: String,
    val businessGuid: UUID,
    val hold: Balance,
    val balance: Balance,
    val available: Balance,
    val creditLimit: Money,
    val creditTerms: String,
    val status: Status,
    val type: String,
  ) : CompleteRep {

    internal class Deserializer : StdDeserializer<Complete>(Complete::class.java) {
      override fun deserialize(p: JsonParser, ctxt: DeserializationContext): Complete {
        val tree = p.readValueAsTree<JsonNode>()
        val attributes = tree.get("attributes")
        val tags = attributes.get("tags")

        return Complete(
          id = tree.readValueNotNull(ctxt, "id"),
          name = tags.readValueNotNull(ctxt, "name"),
          businessGuid = tags.readValueNotNull(ctxt, "businessGuid"),
          hold = attributes.readValueNotNull(ctxt, "hold"),
          balance = attributes.readValueNotNull(ctxt, "balance"),
          available = attributes.readValueNotNull(ctxt, "available"),
          creditLimit = attributes.readValueNotNull(ctxt, "creditLimit"),
          creditTerms = attributes.readValueNotNull(ctxt, "creditTerms"),
          status = attributes.readValueNotNull(ctxt, "status"),
          type = tree.readValueNotNull(ctxt, "type")
        )
      }
    }
  }
}
