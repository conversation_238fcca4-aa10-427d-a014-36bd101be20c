package co.highbeam.sql.argumentFactory

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import org.jdbi.v3.core.argument.AbstractArgumentFactory
import org.jdbi.v3.core.argument.Argument
import org.jdbi.v3.core.config.ConfigRegistry
import java.sql.Types

internal class JsonNodeArgumentFactory(
  private val objectMapper: ObjectMapper,
) : AbstractArgumentFactory<JsonNode>(Types.VARCHAR) {
  override fun build(value: JsonNode, config: ConfigRegistry): Argument =
    Argument { position, statement, _ ->
      statement.setString(position, objectMapper.writeValueAsString(value))
    }
}
