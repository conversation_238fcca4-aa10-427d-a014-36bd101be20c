package co.highbeam.event

import co.highbeam.event.admin.SubscriptionConfig
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.listener.FakeAckReply
import co.highbeam.event.listener.FakeEventListenerFactory

object FakePubSubProcessor {
  suspend fun <T : Any> process(
    topicConfig: TopicConfig,
    subscriptionConfig: SubscriptionConfig,
    events: List<Pair<T, Map<String, String>>>,
  ): List<FakeAckReply> =
    events.map { (event) ->
      val fakePubSubEventListener = FakeEventListenerFactory.listener<T>(
        topicConfig = topicConfig,
        subscriptionConfig = subscriptionConfig,
      )
      val ackReply = FakeAckReply()
      fakePubSubEventListener.onReceive(event, ackReply)
      return@map ackReply
    }
}

fun List<FakeAckReply>.propagateExceptions() {
  forEach { ackReply ->
    val status = ackReply.status
    if (status is FakeAckReply.Status.Nacked) {
      throw status.e
    }
  }
}
