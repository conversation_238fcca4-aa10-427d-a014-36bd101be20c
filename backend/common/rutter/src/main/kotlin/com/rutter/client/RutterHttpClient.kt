package com.rutter.client

import co.highbeam.client.RealHttpClient
import co.highbeam.client.RequestBuilder
import co.highbeam.metrics.Metrics
import co.highbeam.serialization.HighbeamObjectMapper
import co.highbeam.typeConversion.typeConverter.BalanceLongTypeConverter
import co.highbeam.typeConversion.typeConverter.BalanceStringTypeConverter
import co.highbeam.typeConversion.typeConverter.DEFAULT_TYPE_CONVERTERS
import co.highbeam.typeConversion.typeConverter.MoneyLongTypeConverter
import co.highbeam.typeConversion.typeConverter.MoneyStringTypeConverter
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.PropertyNamingStrategies

/**
 * Timeout should be in sync with the Shopify request timeouts.
 */
private const val REQUEST_TIMEOUT_MILLIS = 120_000L

class RutterHttpClient(
  baseUrl: String,
  version: String,
  metrics: Metrics,
) : RealHttpClient(
  baseUrl = baseUrl,
  metrics = metrics,
  objectMapper = HighbeamObjectMapper.json {
    useNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
    allowUnknownProperties(true)
    // Rutter currently returns list for Amazon balances and single value for other platforms
    enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    useTypeConverters(DEFAULT_TYPE_CONVERTERS
      - BalanceLongTypeConverter + BalanceStringTypeConverter
      - MoneyLongTypeConverter + MoneyStringTypeConverter
    )
  }.build(),
  requestTimeoutMillis = REQUEST_TIMEOUT_MILLIS,
) {
  override val rootBuilder: RequestBuilder = {
    putHeader("X-Rutter-Version", version)
  }
}
