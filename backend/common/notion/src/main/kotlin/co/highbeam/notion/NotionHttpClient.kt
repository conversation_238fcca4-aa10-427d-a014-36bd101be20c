package co.highbeam.notion

import co.highbeam.client.RealHttpClient
import co.highbeam.client.RequestBuilder
import co.highbeam.metrics.Metrics
import co.highbeam.protectedString.ProtectedString
import co.highbeam.serialization.HighbeamObjectMapper
import co.highbeam.typeConversion.typeConverter.DEFAULT_TYPE_CONVERTERS
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import io.ktor.http.HttpHeaders

class NotionHttpClient(
  baseUrl: String,
  metrics: Metrics,
  notionVersion: String,
  private val secret: ProtectedString
) : RealHttpClient(
  baseUrl = baseUrl,
  metrics = metrics,
  objectMapper = HighbeamObjectMapper.json {
    useNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
    allowUnknownProperties(true)
    useTypeConverters(DEFAULT_TYPE_CONVERTERS)
  }.build(),
) {
  override val rootBuilder: RequestBuilder = {
    putHeader(HttpHeaders.Authorization, "Bearer ${secret.value}")
    putHeader("Notion-Version", notionVersion)
  }
}
