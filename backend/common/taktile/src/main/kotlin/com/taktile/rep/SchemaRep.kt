package com.taktile.rep

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.util.UUID

interface CreatorRep

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
object SchemaRep {
  data class Creator(
    @JsonProperty("business_guid") val businessGuid: UUID,
    @JsonProperty("credit_application_guid") val creditApplicationGuid: UUID,
    @JsonProperty("business_name") val businessName: String?,
    @JsonProperty("business_dba") val businessDba: String?,
    @JsonProperty("phone_number") val phoneNumber: String?,
    @JsonProperty("ein") val ein: String,
    @JsonProperty("incorporation_state") val incorporationState: String,
    @JsonProperty("associated_person") val associatedPerson: String,
    @JsonProperty("address") val address: AddressRep,
  ) : CreatorRep

  data class AddressRep(
    @JsonProperty("line1") val line1: String,
    @JsonProperty("line2") val line2: String?,
    @JsonProperty("city") val city: String?,
    @JsonProperty("state") val state: String,
    @JsonProperty("postal_code") val postalCode: String?,
    @JsonProperty("country") val country: String,
  )
}
