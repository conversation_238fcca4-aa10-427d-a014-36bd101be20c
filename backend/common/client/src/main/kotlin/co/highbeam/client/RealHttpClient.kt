package co.highbeam.client

import co.highbeam.metrics.Metrics
import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.client.engine.HttpClientEngineFactory
import io.ktor.client.engine.cio.CIO
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logging
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.header
import io.ktor.client.request.request
import io.ktor.client.request.setBody
import io.ktor.client.request.url
import io.ktor.http.ContentType
import io.ktor.http.HttpMethod
import io.ktor.http.contentType
import mu.KotlinLogging
import io.ktor.client.HttpClient as KtorHttpClient

/**
 * The primary constructor should only be used for testing. There's no need to specify an engine
 * factory in production code - [CIO] will be used.
 */
open class RealHttpClient internal constructor(
  engineFactory: HttpClientEngineFactory<*>,
  private val baseUrl: String,
  private val metrics: Metrics,
  objectMapper: ObjectMapper,
  connectTimeoutMillis: Long,
  requestTimeoutMillis: Long,
  retry: Boolean,
) : HttpClient(objectMapper) {
  constructor(
    baseUrl: String,
    metrics: Metrics,
    objectMapper: ObjectMapper,
    connectTimeoutMillis: Long = 5_000,
    requestTimeoutMillis: Long = 15_000,
    retry: Boolean = false,
  ) : this(CIO, baseUrl, metrics, objectMapper, connectTimeoutMillis, requestTimeoutMillis, retry)

  private val logger = KotlinLogging.logger {}

  private val httpClient: KtorHttpClient = KtorHttpClient(engineFactory) {
    install(HttpTimeout) {
      this.connectTimeoutMillis = connectTimeoutMillis
      this.requestTimeoutMillis = requestTimeoutMillis
    }
    if (retry) {
      install(HttpRequestRetry) {
        retryOnException(maxRetries = 5)
        constantDelay(millis = 4_000)
      }
    }
    install(Logging) {
      level = LogLevel.ALL
      logger = object : io.ktor.client.plugins.logging.Logger {
        override fun log(message: String) {
          <EMAIL> { "KTOR CLIENT: $message" }
        }
      }
    }
  }


  override suspend fun request(
    httpMethod: HttpMethod,
    path: String,
    qp: Map<String, List<String>>,
    accept: ContentType,
    body: Any?,
    builder: RequestBuilder?,
  ): HttpResponse {
    val sample = metrics.timeSample()
    val httpResponse = httpClient.request {
      val requestBuilder = HighbeamHttpClientRequestBuilder(
        httpMethod = httpMethod,
        baseUrl = baseUrl,
        path = path,
        qp = qp,
        accept = accept
      ).apply {
        rootBuilder?.let { it() }
        builder?.let { it() }
      }
      setHttpMethod(requestBuilder.httpMethod)
      setUrl(requestBuilder.url)
      setHeaders(requestBuilder.headers)
      val serializedBody = setBody(body, requestBuilder.contentType)

      // Log the complete request details
      logger.info {
        buildString {
          appendLine("=== HTTP REQUEST ===")
          appendLine("Method: ${requestBuilder.httpMethod.value}")
          appendLine("URL: ${requestBuilder.url}")
          appendLine("Headers:")
          requestBuilder.headers.forEach { (key, value) ->
            appendLine("  $key: $value")
          }
          if (serializedBody != null) {
            appendLine("Body:")
            appendLine(serializedBody)
          }
          appendLine("===================")
        }
      }
    }
    val timer = metrics.timer(
      "http.client.request",
      "method", httpMethod.value,
      "baseUrl", baseUrl,
      "statusCode", httpResponse.status.value.toString(),
      "status", httpResponse.status.toString(),
    )
    sample.stop(timer)

    return RealHttpResponse(httpResponse, objectMapper)
  }

  private fun HttpRequestBuilder.setHttpMethod(httpMethod: HttpMethod) {
    method = httpMethod
  }

  private fun HttpRequestBuilder.setUrl(url: String) {
    url(url)
  }

  private fun HttpRequestBuilder.setHeaders(headers: Map<String, String>) {
    headers.forEach { (key, value) -> header(key, value) }
  }

  private fun HttpRequestBuilder.setBody(body: Any?, contentType: ContentType): String? {
    return body?.let {
      contentType(contentType)
      val serializedBody = if (it is String) it else objectMapper.writeValueAsString(it)
      setBody(serializedBody)
      serializedBody
    }
  }

  protected open val rootBuilder: RequestBuilder? = null

  override fun close(): Unit = httpClient.close()
}
