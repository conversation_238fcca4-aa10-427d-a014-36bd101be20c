package co.highbeam.client

import co.highbeam.metrics.Metrics
import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.client.engine.HttpClientEngineFactory
import io.ktor.client.engine.cio.CIO
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.header
import io.ktor.client.request.request
import io.ktor.client.request.setBody
import io.ktor.client.request.url
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpMethod
import io.ktor.http.contentType
import mu.KotlinLogging
import io.ktor.client.HttpClient as KtorHttpClient

/**
 * The primary constructor should only be used for testing. There's no need to specify an engine
 * factory in production code - [CIO] will be used.
 */
open class RealHttpClient internal constructor(
  engineFactory: HttpClientEngineFactory<*>,
  private val baseUrl: String,
  private val metrics: Metrics,
  objectMapper: ObjectMapper,
  connectTimeoutMillis: Long,
  requestTimeoutMillis: Long,
  retry: Boolean,
) : HttpClient(objectMapper) {
  private val logger = KotlinLogging.logger {}
  constructor(
    baseUrl: String,
    metrics: Metrics,
    objectMapper: ObjectMapper,
    connectTimeoutMillis: Long = 5_000,
    requestTimeoutMillis: Long = 15_000,
    retry: Boolean = false,
  ) : this(CIO, baseUrl, metrics, objectMapper, connectTimeoutMillis, requestTimeoutMillis, retry)

  private val httpClient: KtorHttpClient = KtorHttpClient(engineFactory) {
    install(HttpTimeout) {
      this.connectTimeoutMillis = connectTimeoutMillis
      this.requestTimeoutMillis = requestTimeoutMillis
    }
    if (retry) {
      install(HttpRequestRetry) {
        retryOnException(maxRetries = 5)
        constantDelay(millis = 4_000)
      }
    }
  }


  override suspend fun request(
    httpMethod: HttpMethod,
    path: String,
    qp: Map<String, List<String>>,
    accept: ContentType,
    body: Any?,
    builder: RequestBuilder?,
  ): HttpResponse {
    val sample = metrics.timeSample()

    val requestBuilder = HighbeamHttpClientRequestBuilder(
      httpMethod = httpMethod,
      baseUrl = baseUrl,
      path = path,
      qp = qp,
      accept = accept
    ).apply {
      rootBuilder?.let { it() }
      builder?.let { it() }
    }

    // Debug logging for request details
    logger.info { "HTTP Request: [method=${requestBuilder.httpMethod}, url=${requestBuilder.url}]" }
    logger.info { "HTTP Request headers: ${requestBuilder.headers.map { "${it.key}: ${it.value}" }}" }
    logger.info { "HTTP Request body: $body" }

    val httpResponse = httpClient.request {
      setHttpMethod(requestBuilder.httpMethod)
      setUrl(requestBuilder.url)
      setHeaders(requestBuilder.headers)
      setBody(body, requestBuilder.contentType)
    }
    // Debug logging for response details
    logger.info { "HTTP Response: [statusCode=${httpResponse.status}, headers=${httpResponse.headers.entries().map { "${it.key}: ${it.value}" }}]" }

    // Log response body for debugging (be careful with sensitive data)
    try {
      val responseBodyText = httpResponse.bodyAsText()
      logger.info { "HTTP Response body: $responseBodyText" }
    } catch (e: Exception) {
      logger.warn { "Failed to read response body for logging: ${e.message}" }
    }

    val timer = metrics.timer(
      "http.client.request",
      "method", httpMethod.value,
      "baseUrl", baseUrl,
      "statusCode", httpResponse.status.value.toString(),
      "status", httpResponse.status.toString(),
    )
    sample.stop(timer)

    return RealHttpResponse(httpResponse, objectMapper)
  }

  private fun HttpRequestBuilder.setHttpMethod(httpMethod: HttpMethod) {
    method = httpMethod
  }

  private fun HttpRequestBuilder.setUrl(url: String) {
    url(url)
  }

  private fun HttpRequestBuilder.setHeaders(headers: Map<String, String>) {
    headers.forEach { (key, value) -> header(key, value) }
  }

  private fun HttpRequestBuilder.setBody(body: Any?, contentType: ContentType) {
    body?.let {
      contentType(contentType)
      setBody(run {
        if (it is String) return@run it
        return@run objectMapper.writeValueAsString(it)
      })
    }
  }

  protected open val rootBuilder: RequestBuilder? = null

  override fun close(): Unit = httpClient.close()
}
