package co.highbeam.capital.onboarding.model.application

import co.highbeam.rep.onboarding.CapitalApplicationDocumentRep
import com.fasterxml.jackson.annotation.JsonInclude
import org.jdbi.v3.json.Json
import java.util.UUID

data class CapitalApplicationDocumentModel(
  val guid: UUID,
  val businessGuid: UUID,
  val capitalApplicationGuid: UUID,
  val type: CapitalApplicationDocumentRep.Type,
  @Json val data: Data,
) {
  @JsonInclude(JsonInclude.Include.NON_NULL)
  data class Data(
    val fileName: String,
    val notes: String?,
  )
}

