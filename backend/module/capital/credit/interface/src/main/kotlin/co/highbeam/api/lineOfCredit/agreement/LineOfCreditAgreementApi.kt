package co.highbeam.api.lineOfCredit.agreement

import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod
import java.util.UUID

// TODO(justin): Consolidate everything inside of co.highbeam.api.onboarding.CreditAgreementApi
//  into here. Credit agreement is no longer only a part of onboarding.
object LineOfCreditAgreementApi {

  data class GetMetadata(
    val businessGuid: UUID,
    val lineOfCreditGuid: UUID,
  ) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/businesses/$businessGuid/lines-of-credit/$lineOfCreditGuid/" +
      "line-of-credit-agreements/metadata"
  )

  data class GetAgreements(
    val businessGuid: UUID,
    val lineOfCreditGuid: UUID,
  ) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/businesses/$businessGuid/lines-of-credit/$lineOfCreditGuid/line-of-credit-agreements"
  )
}
