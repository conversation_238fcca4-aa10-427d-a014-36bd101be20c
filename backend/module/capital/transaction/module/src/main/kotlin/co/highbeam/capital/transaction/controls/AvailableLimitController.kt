package co.highbeam.capital.transaction.controls

import co.highbeam.money.Balance

class AvailableLimitController : TransactionController {
  override suspend fun shouldAllow(
    data: TransactionControllerData.Drawdown,
    force: Boolean
  ): TransactionControllerData.Status {
    if (force) return TransactionControllerData.Status.Allowed

    val availableLimit = data.account.available
    return if (data.creator.amount <= availableLimit) {
      TransactionControllerData.Status.Allowed
    } else {
      TransactionControllerData.Status.Denied
    }
  }

  override suspend fun shouldAllow(
    data: TransactionControllerData.Repayment,
    force: Boolean
  ): Boolean {
    val expectedBalanceAfterRepayment =
      Balance.fromCents(data.creator.amount.rawCents) + data.account.runningBalance
    return expectedBalanceAfterRepayment <= Balance.ZERO
  }
}
