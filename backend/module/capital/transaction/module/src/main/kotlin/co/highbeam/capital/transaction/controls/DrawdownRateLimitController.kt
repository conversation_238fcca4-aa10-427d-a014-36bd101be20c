package co.highbeam.capital.transaction.controls

import co.highbeam.capital.transaction.ledger.LedgerTransactionService
import co.highbeam.capital.transaction.service.CapitalDrawdownApprovalService
import co.highbeam.money.Balance
import co.highbeam.money.Money
import java.time.Clock
import java.time.ZonedDateTime

class DrawdownRateLimitController(
  private val ledgerTransactionService: LedgerTransactionService,
  private val drawdownApprovalService: CapitalDrawdownApprovalService,
  private val clock: Clock,
) : TransactionController {
  override suspend fun shouldAllow(
    data: TransactionControllerData.Drawdown,
    force: Boolean,
  ): TransactionControllerData.Status {
    if (force) return TransactionControllerData.Status.Allowed

    return data.account.controls.drawdownRateLimit?.let { drawdownRateLimit ->
      if (getRecentTransactionAmount(data) + data.creator.amount <= drawdownRateLimit) {
        return@let TransactionControllerData.Status.Allowed
      }

      if (hasDrawdownApproval(data)) {
        return@let TransactionControllerData.Status.Allowed
      }

      return@let TransactionControllerData.Status.ApprovalNeeded
    } ?: TransactionControllerData.Status.Allowed
  }

  private fun hasDrawdownApproval(data: TransactionControllerData.Drawdown): Boolean {
    val approval = drawdownApprovalService.getApproved(
      businessGuid = data.businessGuid,
      capitalAccountGuid = data.capitalAccountGuid,
    ).singleOrNull {
      it.idempotencyKey == data.creator.idempotencyKey &&
        it.amount == data.creator.amount &&
        it.bankAccountGuid == data.creator.bankAccountGuid
    }
    return approval != null
  }

  private suspend fun getRecentTransactionAmount(data: TransactionControllerData.Drawdown) =
    ledgerTransactionService.transactionsWithTimeFilter(
      businessGuid = data.businessGuid,
      ledgerGuid = data.capitalAccountGuid,
      sinceInclusive = ZonedDateTime.now(clock).minusHours(48),
      untilInclusive = null,
    ).sumOf { it.amount }.takeIf { it < Balance.ZERO }?.abs() ?: Money.ZERO

  override suspend fun shouldAllow(
    data: TransactionControllerData.Repayment,
    force: Boolean,
  ): Boolean = true
}
