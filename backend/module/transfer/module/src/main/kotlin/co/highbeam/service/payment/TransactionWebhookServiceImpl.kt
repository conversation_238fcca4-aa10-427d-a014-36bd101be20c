package co.highbeam.service.payment

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.business.BusinessApi
import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.email.EmailService
import co.highbeam.email.template.EmailTemplate
import co.highbeam.email.template.TransactionCreatedIncomingDebitEmailTemplate
import co.highbeam.email.template.TransactionCreatedIncomingEmailTemplate
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.model.unitCoEvent.UnitCoTransactionCreatedEventModel
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.unit.client.UnitCoClient
import co.unit.rep.UnitCoReceivedAchTransactionRep
import co.unit.rep.UnitCoTransactionRep
import co.unit.rep.UnitCoWireTransactionRep
import co.unit.rep.unitCoCustomerId
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID

internal class TransactionWebhookServiceImpl @Inject constructor(
  private val bankAccountClient: BankAccountClient,
  private val businessClient: BusinessClient,
  private val businessMemberClient: BusinessMemberClient,
  private val emailService: EmailService,
  private val featureFlagService: FeatureFlagService,
  private val unitCoClient: UnitCoClient
) : TransactionWebhookService {
  private val logger = KotlinLogging.logger {}
  override suspend fun handleTransactionCreated(event: UnitCoTransactionCreatedEventModel) {
    // Incoming payment since it increases the balance of the underlying account
    if (event.amount > Balance.ZERO) {
      handleIncomingTransaction(event)
    } else if (event.amount < Balance.ZERO) {
      // Purpose of this is to notify on first ACH debit for new counterparty
      handleIncomingDebitTransaction(event)
    }
  }

  private suspend fun handleIncomingTransaction(event: UnitCoTransactionCreatedEventModel) {
    logger.info { "Incoming transaction. $event" };

    val bankAccount = getBankAccount(event.accountId)
    if (bankAccount == null) {
      logger.info { "Bank account not found for incoming transaction. $event" };
      return
    }
    val business = getBusiness(bankAccount.businessGuid)

    val transactionType = UnitCoTransactionRep.UnitTransactionType.fromValue(event.transactionType)
    val incomingTransactionNotificationEnabled = featureFlagService.isEnabled(
      flag = BusinessFlag.IncomingTransactionNotification,
      businessGuid = business.guid
    )
    val isIncomingWireTransactionNotificationEnabled = featureFlagService.isEnabled(
      flag = BusinessFlag.IncomingWireTransactionNotification,
      businessGuid = business.guid
    )
    val transactionRequiresNotification =
      transactionType == UnitCoTransactionRep.UnitTransactionType.ReceivedAch ||
        transactionType == UnitCoTransactionRep.UnitTransactionType.Wire

    val shouldSendNotification = transactionRequiresNotification && (
      incomingTransactionNotificationEnabled || (
        isIncomingWireTransactionNotificationEnabled &&
          transactionType == UnitCoTransactionRep.UnitTransactionType.Wire
        )
      )

    if (shouldSendNotification) {
      val bankAccount = getBankAccount(event.accountId)
      if (bankAccount == null) {
        logger.info { "Bank account not found for incoming transaction. $event" };
        return
      }
      val businessAdminsRecipients = businessMemberClient.request(
        BusinessMemberApi.GetAdminsByBusiness(business.guid)
      ).mapNotNull {
        it.emailAddress?.let { it1 ->
          EmailTemplate.Recipient(it1, it.fullName)
        }
      }
      val transaction = getUnitCoTransaction(
        accountId = bankAccount.unitCoDepositAccountId,
        event.transactionId
      )
      val senderName = when (transaction) {
        is UnitCoReceivedAchTransactionRep -> transaction.companyName
        is UnitCoWireTransactionRep -> transaction.counterparty.name
        else -> null
      }

      emailService.sync(key = "Incoming payment: ${business.guid} ${event.eventId}") { sendEmail ->
        sendEmail(
          TransactionCreatedIncomingEmailTemplate(
            recipients = businessAdminsRecipients,
            accountName = bankAccount.name,
            accountLast4Digits = bankAccount.accountNumber.takeLast(4),
            amount = Money.fromBalance(event.amount),
            dateReceived = event.createdAt.toLocalDate(),
            description = transaction.summary,
            transactionSenderName = senderName,
            transactionLink = "https://app.highbeam.co/accounts/transactions",
            transactionType = transactionType.displayName,
          )
        )
      }
    }
  }

  private suspend fun handleIncomingDebitTransaction(event: UnitCoTransactionCreatedEventModel) {
    logger.info { "Incoming debit transaction. $event" };

    val bankAccount = getBankAccount(event.accountId)
    if (bankAccount == null) {
      logger.info { "Bank account not found for incoming debit transaction. $event" };
      return
    }
    val business = getBusiness(bankAccount.businessGuid)

    val transactionType = UnitCoTransactionRep.UnitTransactionType.fromValue(event.transactionType)
    val incomingDebitTransactionNotificationEnabled = featureFlagService.isEnabled(
      flag = BusinessFlag.IncomingDebitTransactionNotification,
      businessGuid = business.guid
    )

    if (!(incomingDebitTransactionNotificationEnabled && transactionType ==
        UnitCoTransactionRep.UnitTransactionType.ReceivedAch
        )
    ) {
      return
    }

    val transaction = getUnitCoTransaction(
      accountId = bankAccount.unitCoDepositAccountId,
      event.transactionId
    ) as UnitCoReceivedAchTransactionRep

    // Unit does not allow < in the search query
    // https://highbeamco.slack.com/archives/C02JB5M1599/p1746022810350659
    val senderName = transaction.companyName.replace(
      Regex("[<]"),
      " "
    ).trim()

    val debitTransactions = getUnitCoTransactionList(
      accountId = bankAccount.unitCoDepositAccountId,
      customerId = transaction.unitCoCustomerId,
      direction = "Debit",
      type = "ReceivedAch",
      query = senderName
    )

    if (debitTransactions.size > 1) {
      logger.info {
        "Skipping first time notification for incoming debit transaction." +
          " $event. Transaction has occurred ${debitTransactions.size} times"
      }
      return
    }

    val businessAdminsRecipients = businessMemberClient.request(
      BusinessMemberApi.GetAdminsByBusiness(business.guid)
    ).mapNotNull {
      it.emailAddress?.let { it1 ->
        EmailTemplate.Recipient(it1, it.fullName)
      }
    }

    emailService.sync(
      key = "Incoming payment: ${business.guid} ${event.eventId}"
    ) { sendEmail ->
      sendEmail(
        TransactionCreatedIncomingDebitEmailTemplate(
          recipients = businessAdminsRecipients,
          accountName = bankAccount.name,
          accountLast4Digits = bankAccount.accountNumber.takeLast(4),
          amount = Money.fromBalance(event.amount),
          dateReceived = event.createdAt.toLocalDate(),
          description = transaction.summary,
          transactionSenderName = senderName,
          transactionLink = "https://app.highbeam.co/accounts/transactions",
          transactionType = transactionType.displayName,
        )
      )
    }
  }

  private suspend fun getBusiness(businessGuid: UUID) =
    businessClient.request(BusinessApi.Get(businessGuid)) ?: throw unprocessable(BusinessNotFound())

  private suspend fun getBankAccount(unitCoDepositAccountId: String) =
    bankAccountClient.request(BankAccountApi.GetByUnitCoDepositAccountId(unitCoDepositAccountId))

  private suspend fun getUnitCoTransaction(accountId: String, transactionId: String) =
    unitCoClient.transaction.get(
      accountId = accountId,
      transactionId = transactionId
    ).let(::checkNotNull)

  private suspend fun getUnitCoTransactionList(
    accountId: String,
    customerId: String?,
    direction: String,
    type: String,
    query: String
  ): List<UnitCoReceivedAchTransactionRep> {
    val transactions = unitCoClient.transaction.list(
      accountId = accountId,
      customerId = customerId,
      query = query,
      type = type,
      direction = direction,
      to = null,
      from = null,
      limit = 2,
    )
    return transactions.filterIsInstance<UnitCoReceivedAchTransactionRep>()
  }
}
