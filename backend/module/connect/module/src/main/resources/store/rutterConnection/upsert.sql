insert into connect.rutter_connection (guid, business_guid, rutter_connection_guid, access_token,
                                       shop_name, platform_name, is_ready, is_active,
                                       payouts_activated)
values (:guid, :businessGuid, :rutterConnectionGuid, :accessToken, :shopName, :platformName,
        :isReady, :isActive, :payoutsActivated)
on conflict on constraint uniq__rutter_connection__rutter_connection_guid
  do update
  set access_token     = :accessToken,
      is_ready         = :isReady,
      is_active        = :isActive,
      shop_name        = :shopName,
      payouts_activated = :payoutsActivated
returning *
