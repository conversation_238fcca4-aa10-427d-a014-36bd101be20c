package co.highbeam.service.businessDetails

import co.highbeam.model.businessDetails.BusinessDetailsModel
import co.highbeam.model.businessDetails.InternalBusinessDetailsModel
import com.google.inject.ImplementedBy
import java.util.UUID

@ImplementedBy(BusinessDetailsServiceImpl::class)
internal interface BusinessDetailsService {
  suspend fun get(businessGuid: UUID): BusinessDetailsModel?
  suspend fun getInternal(businessGuid: UUID): InternalBusinessDetailsModel?

  suspend fun update(
    businessGuid: UUID,
    updater: BusinessDetailsModel.Updater,
  ): BusinessDetailsModel
}
