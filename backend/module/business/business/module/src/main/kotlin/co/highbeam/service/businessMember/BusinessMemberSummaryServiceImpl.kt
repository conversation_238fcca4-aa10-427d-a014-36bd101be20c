package co.highbeam.service.businessMember

import co.highbeam.api.userRole.UserRoleApi
import co.highbeam.api.userRole.UserRoleMembershipApi
import co.highbeam.client.userRole.UserRoleClient
import co.highbeam.client.userRole.UserRoleMembershipClient
import co.highbeam.rep.businessMember.BusinessMemberSummaryRep
import co.highbeam.rep.userRole.userRoleName
import co.highbeam.service.business.BusinessService
import com.google.inject.Inject
import java.util.UUID

internal class BusinessMemberSummaryServiceImpl @Inject constructor(
  private val businessService: BusinessService,
  private val businessMemberService: BusinessMemberService,
  private val userRoleClient: UserRoleClient,
  private val userRoleMembershipClient: UserRoleMembershipClient,
) : BusinessMemberSummaryService {
  override suspend fun listByUser(userGuid: UUID): List<BusinessMemberSummaryRep> {
    val businesses = businessService.getByMemberUser(userGuid)
    val businessMembers = businessMemberService.getByUser(userGuid)
    val userRoles = userRoleClient.request(
      UserRoleApi.GetByUser(userGuid)
    )
    val userRoleMemberships = userRoleMembershipClient.request(
      UserRoleMembershipApi.GetByUser(userGuid),
    )

    return businesses.map { business ->
      val businessMember = businessMembers.single { businessMember ->
        businessMember.businessGuid == business.guid
      }
      val userRoleMemberships = userRoleMemberships.filter { userRoleMembership ->
        userRoleMembership.businessGuid == business.guid
      }
      val userRoles = userRoles.filter { userRole ->
        userRole.guid in userRoleMemberships.map { it.userRoleGuid }
      }
      return@map BusinessMemberSummaryRep(
        businessDisplayName = business.displayName,
        businessGuid = business.guid,
        businessStatus = business.status,
        isOwner = business.ownerUserGuid == userGuid,
        userGuid = businessMember.userGuid,
        userIsOnboarded = businessMember.isOnboarded,
        userRoleName = userRoleName(userRoles),
      )
    }.sortedWith(BusinessMemberSummaryRep.sorter)
  }
}
