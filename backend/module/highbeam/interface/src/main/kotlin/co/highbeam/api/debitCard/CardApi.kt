package co.highbeam.api.debitCard

import co.highbeam.rep.debitCard.CardRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.ContentType
import io.ktor.http.HttpMethod
import java.time.ZonedDateTime
import java.util.UUID
import co.unit.rep.CardRep as UnitCoCardRep

object CardApi {
  data class Post(val rep: CardRep.Creator) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/debit-cards",
    body = rep,
  )

  data class Patch(
    val businessGuid: UUID,
    val cardId: String,
    val rep: CardRep.Updater,
  ) : Endpoint(
    httpMethod = HttpMethod.Patch,
    path = "/businesses/$businessGuid/debit-cards/$cardId",
    body = rep,
  )

  data class GetAll(
    val businessGuid: UUID,
    val userGuid: UUID,
    val unitCoAccountId: String? = null,
    val status: List<UnitCoCardRep.Status>? = null,
  ) : Endpoint(
    path = "/cards",
    qp = buildMap {
      put("businessGuid", listOf(businessGuid.toString()))
      put("userGuid", listOf(userGuid.toString()))
      status?.let { put("status", status.map { it.toString() }) }
      unitCoAccountId?.let { put("unitCoAccountId", listOf(unitCoAccountId)) }
    },
  )

  data class TransactionsCsv(
    val businessGuid: UUID,
    val cardId: String?,
    val query: String?,
    val from: ZonedDateTime?,
    val to: ZonedDateTime?,
  ) : Endpoint(
    path = "/cards/$cardId/transactions",
    qp = buildMap {
      put("businessGuid", listOf(businessGuid.toString()))
      query?.let { put("query", listOf(query)) }
      from?.let { put("from", listOf(from.toString())) }
      to?.let { put("to", listOf(to.toString())) }
    },
    contentType = ContentType.Text.CSV,
  )

  data class AllTransactionsCsv(
    val businessGuid: UUID,
    val query: String?,
    val from: ZonedDateTime?,
    val to: ZonedDateTime?,
  ) : Endpoint(
    path = "/cards/transactions",
    qp = buildMap {
      put("businessGuid", listOf(businessGuid.toString()))
      query?.let { put("query", listOf(query)) }
      from?.let { put("from", listOf(from.toString())) }
      to?.let { put("to", listOf(to.toString())) }
    },
    contentType = ContentType.Text.CSV,
  )

  data class Close(
    val cardId: String
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/cards/$cardId/close",
  )
}
