package co.highbeam.service.transaction

import co.highbeam.api.business.BusinessApi
import co.highbeam.client.business.BusinessClient
import co.unit.client.UnitCoClient
import co.unit.rep.TransactionAccountType
import com.fasterxml.jackson.databind.JsonNode
import com.google.inject.Inject
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.UUID

internal class TransactionServiceImpl @Inject constructor(
  private val businessClient: BusinessClient,
  private val unitCoClient: UnitCoClient,
) : TransactionService {
  override suspend fun search(
    businessGuid: UUID,
    unitCoDepositAccountId: String?,
    query: String?,
    from: ZonedDateTime?, // Deprecated - use fromStatementDate instead
    to: ZonedDateTime?, // Deprecated - use toStatementDateInclusive instead
    fromStatementDate: LocalDate?,
    toStatementDateInclusive: LocalDate?,
    accountType: TransactionAccountType?,
    cardId: String?,
    types: List<String>?,
    sort: String?,
  ): Flow<JsonNode> {
    val business = checkNotNull(businessClient.request(BusinessApi.Get(businessGuid)))
    val resolvedFrom = if (fromStatementDate != null) {
      localDateToStatementDateStartTime(fromStatementDate)
    } else {
      from
    }
    val resolvedTo = if (toStatementDateInclusive != null) {
      localDateToStatementDateStartTime(
        toStatementDateInclusive.plusDays(1)
      )
    } else {
      to
    }
    return unitCoClient.transaction.list(
      customerId = checkNotNull(business.unitCoCustomerId),
      accountId = unitCoDepositAccountId,
      query = query,
      from = resolvedFrom,
      to = resolvedTo,
      accountType = accountType,
      cardId = cardId,
      types = types,
      sort = sort,
    )
  }

  /**
   * Thread Bank's end-of-day cutoff is 5PM ET, so we filter transaction dates based on that cutoff.
   *
   * https://highbeamco.slack.com/archives/C02JB5M1599/p1740754535335979
   *
   * https://www.unit.co/docs/api/deposit-accounts/#get-account-balance-history
   */
  private fun localDateToStatementDateStartTime(localDate: LocalDate): ZonedDateTime =
    localDate
      .minusDays(1)
      .atTime(17, 0)
      .atZone(ZoneId.of("America/New_York"))
}
