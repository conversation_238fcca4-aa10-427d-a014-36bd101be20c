package co.highbeam.publisher.bankAccounts

import co.highbeam.event.PlaidAccountDeactivatedEvent
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.event.publisher.EventPublisherFactory
import co.highbeam.event.publisher.PublisherProvider
import co.highbeam.feature.typeLiteral
import com.google.inject.Inject

internal const val PLAID_ACCOUNT_DEACTIVATED = "plaid-account-deactivated"

internal class PlaidAccountDeactivatedPublisherFactory @Inject constructor(
  private val factory: EventPublisherFactory,
) : PublisherProvider<EventPublisher<PlaidAccountDeactivatedEvent>>() {

  override fun get(): EventPublisher<PlaidAccountDeactivatedEvent> =
    factory.buildPublisher(topic)


  companion object : PublisherProvider.Companion<PlaidAccountDeactivatedEvent>(
    topic = TopicConfig(PLAID_ACCOUNT_DEACTIVATED),
    typeLiteral = typeLiteral(),
    provider = PlaidAccountDeactivatedPublisherFactory::class,
  )
}
