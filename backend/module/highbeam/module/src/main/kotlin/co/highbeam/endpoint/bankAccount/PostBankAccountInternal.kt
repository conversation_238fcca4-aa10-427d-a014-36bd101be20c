package co.highbeam.endpoint.bankAccount

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.exception.bankAccount.UnitCoDepositAccountNotFound
import co.highbeam.mapper.bankAccount.BankAccountMapper
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.bankAccount.BankAccountService
import co.highbeam.service.bankAccount.BankAccountStopPaymentService
import co.unit.client.UnitCoClient
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.bankAccount.BankAccountApi as Api
import co.highbeam.rep.bankAccount.BankAccountRep as Rep

internal class PostBankAccountInternal @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val bankAccountMapper: Bank<PERSON><PERSON>untMapper,
  private val bankAccountService: BankAccountService,
  private val bankAccountStopPaymentService: BankAccountStopPaymentService,
  private val unitCoClient: UnitCoClient,
) : EndpointHandler<Api.PostInternal, Rep.Complete>(
  template = Api.PostInternal::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.PostInternal =
    Api.PostInternal(rep = call.body())

  override suspend fun Handler.handle(endpoint: Api.PostInternal): Rep.Complete {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    val bankAccount = bankAccountService.create(
      bankAccountMapper.creatorModel(endpoint.rep)
    )
    val unitCoBankAccount = unitCoClient.depositAccount.get(bankAccount.unitCoDepositAccountId)
      ?: throw UnitCoDepositAccountNotFound()

    if (bankAccount.type == BankAccountRep.Type.HighYield) {
      bankAccountStopPaymentService.stopAllAchDebitPayments(bankAccount.guid)
    }
    return bankAccountMapper.completeRep(bankAccount, unitCoBankAccount)
  }
}
