package co.highbeam.service.unitCoEventHandler

import co.highbeam.model.bankAccount.BankAccountModel
import co.highbeam.model.unitCoEvent.UnitCoAccountClosedEventModel
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.service.bankAccount.BankAccountService
import com.google.inject.Inject

@Deprecated("""DEPRECATION NOTICE:

PLEASE DO NOT ADD ANY MORE CODE TO THIS.
Webhook handling can now be distributed using Pub/Sub.
You can handle webhook events in your module. No need to do it here.
Search for [topic = "unit-co-webhook"] for an example."""
)
internal class UnitCoAccountClosedEventHandler @Inject constructor(
  private val bankAccountService: BankAccountService,
) {
  fun handleEvent(event: UnitCoAccountClosedEventModel) {
    bankAccountService.update(
      accountGuid = event.bankAccountGuid,
      updater = BankAccountModel.Updater(status = BankAccountRep.Status.CLOSED),
    )
  }
}
