package co.highbeam.endpoint.bankAccount

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.bankAccount.BankAccountRepFixture
import co.highbeam.server.Server
import co.highbeam.testing.BankAccountIntegrationTest
import co.unit.rep.DepositAccountRep
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class GetBankAccountByUnitCoDepositAccountIdTest(
  server: Server<*>,
) : BankAccountIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()

  @BeforeEach
  fun setup() {
    createBankAccountSetting(businessGuid)
  }

  @Test
  fun `bank account does not exist`() = integrationTest {
    val unitCoDepositAccountId = UUID.randomUUID().toString()

    assertThat(
      bankAccountClient.request(
        BankAccountApi.GetByUnitCoDepositAccountId(unitCoDepositAccountId),
      )
    ).isNull()
  }

  @Test
  fun `bank account exists`() = integrationTest {

    val accountRep = BankAccountRepFixture(this@GetBankAccountByUnitCoDepositAccountIdTest)
      .complete(
        idSeed = 1,
        unitCoDepositAccountId = uuidGenerator[1].toString(),
        businessGuid = businessGuid,
        isPrimary = false,
      )

    mockBusiness(businessGuid)
    mockUnitCoAccountGet(businessGuid = businessGuid, bankAccountGuid = accountRep.guid)

    bankAccountClient.request(
      BankAccountApi.Post(
        rep = BankAccountRepFixture(this@GetBankAccountByUnitCoDepositAccountIdTest)
          .creator(businessGuid),
      ),
    )

    assertThat(
      bankAccountClient.request(
        BankAccountApi.GetByUnitCoDepositAccountId(accountRep.unitCoDepositAccountId),
      ),
    )
      .isEqualTo(accountRep)
  }

  @Test
  fun `bank account state is different on Unit`() = integrationTest {
    val bankAccountCreationRep =
      BankAccountRepFixture(this@GetBankAccountByUnitCoDepositAccountIdTest)
        .creator(businessGuid)
    val expectedAccountRep =
      BankAccountRepFixture(this@GetBankAccountByUnitCoDepositAccountIdTest)
        .complete(
          idSeed = 1,
          unitCoDepositAccountId = uuidGenerator[1].toString(),
          businessGuid = businessGuid,
          isPrimary = false,
          status = BankAccountRep.Status.CLOSED,
          highbeamType = BankAccountRep.Type.HighYield,
          depositProduct = DepositAccountRep.DepositProduct.HighYield,
        )

    mockBusiness(businessGuid)
    mockUnitCoAccountGet(
      businessGuid = businessGuid,
      bankAccountGuid = expectedAccountRep.guid,
      status = DepositAccountRep.Status.Closed,
      depositProduct = DepositAccountRep.DepositProduct.HighYield,
    )

    bankAccountClient.request(BankAccountApi.Post(rep = bankAccountCreationRep))

    assertThat(
      bankAccountClient.request(
        BankAccountApi.GetByUnitCoDepositAccountId(expectedAccountRep.unitCoDepositAccountId),
      ),
    ).isEqualTo(expectedAccountRep)
  }
}
