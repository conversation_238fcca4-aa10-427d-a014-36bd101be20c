package co.highbeam.rep.treasury.rule

import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.treasury.command.TreasuryCommandConfig
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import java.math.BigDecimal
import java.util.UUID

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
  JsonSubTypes.Type(Rule.CapitalBasedFulfillment::class, name = "CapitalBasedFulfillment"),
  JsonSubTypes.Type(Rule.ChargeCardLimitSync::class, name = "ChargeCardLimitSync"),
  JsonSubTypes.Type(Rule.CashCardRepayment::class, name = "CashCardRepayment"),
  JsonSubTypes.Type(Rule.ChargeCardRepayment::class, name = "ChargeCardRepayment"),
  JsonSubTypes.Type(Rule.CreditInstallment::class, name = "CreditInstallment"),
  JsonSubTypes.Type(Rule.CapitalInstallmentRetry::class, name = "CapitalInstallmentRetry"),
  JsonSubTypes.Type(
    Rule.DepositAccountLowBalanceAlert::class, name = "DepositAccountLowBalanceAlert"
  ),
  JsonSubTypes.Type(Rule.FractionalSweep::class, name = "FractionalSweep"),
  JsonSubTypes.Type(Rule.MaximumBalance::class, name = "MaximumBalance"),
  JsonSubTypes.Type(Rule.MinimumOperatingBalance::class, name = "MinimumOperatingBalance"),
  JsonSubTypes.Type(Rule.Command::class, name = "Command"),
)
sealed class Rule {
  data class CapitalBasedFulfillment(
    val capitalAccountGuid: UUID,
  ) : Rule()

  data class ChargeCardLimitSync(
    val capitalAccountGuid: UUID,
  ) : Rule()

  data class CashCardRepayment(
    val capitalAccountGuid: UUID,
  ) : Rule()

  data class ChargeCardRepayment(
    val bankAccountGuid: UUID? = null,
    val capitalAccountGuid: UUID,
    val threshold: Balance,
    val balanceMultiplier: BigDecimal,
    val createDrawdown: Boolean,
  ) : Rule()

  data class CreditInstallment(
    val bankAccountGuid: UUID? = null,
    val creditAccountGuid: UUID,
    val amountStrategy: AmountStrategy,
    val pullPartialPayments: Boolean = false,
    val retryPolicy: RetryPolicy = RetryPolicy.AllOrNothing
  ) : Rule() {

    enum class RetryPolicy {
      None, PartialAllowed, AllOrNothing
    }

    @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
    @JsonSubTypes(
      JsonSubTypes.Type(AmountStrategy.LimitMultiplier::class, name = "LimitMultiplier"),
      JsonSubTypes.Type(AmountStrategy.MaturityBased::class, name = "MaturityBased"),
      JsonSubTypes.Type(AmountStrategy.ProRata::class, name = "ProRata"),
      JsonSubTypes.Type(AmountStrategy.UnpaidDrawdown::class, name = "UnpaidDrawdown"),
    )
    sealed class AmountStrategy {
      data class LimitMultiplier(val multiplier: BigDecimal) : AmountStrategy()

      data class MaturityBased(val netDays: Long) : AmountStrategy()

      data class ProRata(val multiplier: BigDecimal) : AmountStrategy()

      data class UnpaidDrawdown(val multiplier: BigDecimal) : AmountStrategy()
    }
  }

  data class CapitalInstallmentRetry(
    val bankAccountGuid: UUID? = null,
    val capitalAccountGuid: UUID,
    val amount: Money,
    val pullPartialPayments: Boolean,
  ) : Rule()

  data class DepositAccountLowBalanceAlert(
    val bankAccountGuid: UUID,
    val lowBalanceAlert: Balance,
  ) : Rule()

  data class FractionalSweep(
    val bankAccountGuid: UUID? = null,
    val maximumBalance: Money,
    val bankAccountFractions: List<BankAccountFraction>,
    val description: String? = null,
  ) : Rule() {
    data class BankAccountFraction(
      val bankAccountGuid: UUID,
      val fraction: BigDecimal,
    )
  }

  data class MaximumBalance(
    val maximumBalance: Money,
    val bankAccountGuid: UUID? = null,
    val destinationBankAccountGuid: UUID? = null,
  ) : Rule()

  data class MinimumOperatingBalance(
    val minimumOperatingBalance: Money,
    val bankAccountGuid: UUID? = null,
    val sourceBankAccountGuid: UUID? = null,
  ) : Rule()

  data class Command(
    val command: TreasuryCommandConfig,
  ) : Rule()
}
