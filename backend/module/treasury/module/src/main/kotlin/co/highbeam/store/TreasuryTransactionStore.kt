package co.highbeam.store

import co.highbeam.model.TreasuryTransactionModel
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import com.google.inject.Singleton
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.bindKotlin
import java.util.UUID

@Singleton
class TreasuryTransactionStore @Inject constructor(jdbi: Jdbi) : SqlStore(jdbi) {
  fun create(
    creator: TreasuryTransactionModel.Creator,
  ): TreasuryTransactionModel = transaction { handle ->
    val query = handle.createQuery(sqlResource("store/transaction/create.sql"))
    query.bindKotlin(creator)
    return@transaction query.mapTo(TreasuryTransactionModel::class.java).single()
  }

  fun update(
    treasuryTransactionModelUpdater: TreasuryTransactionModel.Updater,
  ): TreasuryTransactionModel? = transaction { handle ->
    val query = handle.createQuery(sqlResource("store/transaction/update.sql"))
    query.bindKotlin(treasuryTransactionModelUpdater)
    return@transaction query.mapTo(TreasuryTransactionModel::class.java).singleNullOrThrow()
  }

  fun getForRule(ruleGuid: UUID): List<TreasuryTransactionModel> = handle { handle ->
    val query = handle.createQuery(sqlResource("store/transaction/getForRule.sql"))
    query.bind("ruleGuid", ruleGuid)
    return@handle query.mapTo(TreasuryTransactionModel::class.java).toList()
  }

  fun getForBusiness(businessGuid: UUID): List<TreasuryTransactionModel> = handle { handle ->
    val query = handle.createQuery(sqlResource("store/transaction/getForBusiness.sql"))
    query.bind("businessGuid", businessGuid)
    return@handle query.mapTo(TreasuryTransactionModel::class.java).toList()
  }

}
