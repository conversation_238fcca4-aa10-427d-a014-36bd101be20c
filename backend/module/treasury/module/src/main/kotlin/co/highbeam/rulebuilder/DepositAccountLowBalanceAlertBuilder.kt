package co.highbeam.rulebuilder

import co.highbeam.command.TreasuryCommand
import co.highbeam.command.TreasuryCommandConfigMapper
import co.highbeam.rep.treasury.bankAccount.BankAccountSelector
import co.highbeam.rep.treasury.command.TreasuryCommandConfig
import co.highbeam.rep.treasury.ruleTrigger.TriggerData
import com.google.inject.Inject
import java.util.UUID
import co.highbeam.rep.treasury.rule.Rule.DepositAccountLowBalanceAlert as RuleConfig

data class DepositAccountLowBalanceAlertBuilder @Inject constructor(
  private val treasuryCommandConfigMapper: TreasuryCommandConfigMapper,
) : RuleBuilder<RuleConfig>() {
  override suspend fun build(rule: RuleConfig, businessGuid: UUID): List<TreasuryCommand> {
    return listOf(
      treasuryCommandConfigMapper.getTreasuryCommand(
        TreasuryCommandConfig.AccountLowBalanceEmail(
          account = BankAccountSelector.Highbeam(rule.bankAccountGuid),
          lowBalanceAlert = rule.lowBalanceAlert,
        )
      )
    )
  }

  override suspend fun build(
    rule: RuleConfig,
    businessGuid: UUID,
    triggerData: TriggerData
  ) =
    build(rule, businessGuid)
}
