package co.highbeam.rulebuilder

import co.highbeam.command.TreasuryCommand
import co.highbeam.command.TreasuryCommandConfigMapper
import co.highbeam.rep.treasury.ruleTrigger.TriggerData
import com.google.inject.Inject
import java.util.UUID
import co.highbeam.rep.treasury.rule.Rule.Command as RuleConfig

class GenericCommandRuleBuilder @Inject constructor(
  private val treasuryCommandConfigMapper: TreasuryCommandConfigMapper,
) : RuleBuilder<RuleConfig>() {
  override suspend fun build(rule: RuleConfig, businessGuid: UUID): List<TreasuryCommand> {
    return listOf(
      treasuryCommandConfigMapper.getTreasuryCommand(rule.command)
    )
  }

  override suspend fun build(
    rule: RuleConfig,
    businessGuid: UUID,
    triggerData: TriggerData
  ) =
    build(rule, businessGuid)
}
