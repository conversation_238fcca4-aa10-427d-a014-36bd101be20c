package co.highbeam.rulebuilder

import co.highbeam.amount.AmountConfigMapper
import co.highbeam.command.TreasuryCommand
import co.highbeam.command.TreasuryCommandConfigMapper
import co.highbeam.rep.treasury.amount.AmountSelector
import co.highbeam.rep.treasury.bankAccount.BankAccountSelector
import co.highbeam.rep.treasury.command.TreasuryCommandConfig
import co.highbeam.rep.treasury.ruleTrigger.TriggerData
import com.google.inject.Inject
import java.math.BigDecimal
import java.util.UUID
import co.highbeam.rep.treasury.rule.Rule.FractionalSweep as RuleConfig

data class FractionalSweepBuilder @Inject constructor(
  private val treasuryCommandConfigMapper: TreasuryCommandConfigMapper,
  private val amountConfigMapper: AmountConfigMapper,
) : RuleBuilder<RuleConfig>() {
  override suspend fun build(rule: RuleConfig, businessGuid: UUID): List<TreasuryCommand> {
    require(rule.bankAccountFractions.sumOf { it.fraction } <= BigDecimal.ONE) {
      "Total fraction must be <= 1"
    }

    val principalAmount = amountConfigMapper.getAmountSelection(
      AmountSelector.Surplus(
        threshold = rule.maximumBalance,
        bankAccount = rule.bankAccountGuid?.let { BankAccountSelector.Highbeam(it) }
          ?: BankAccountSelector.Primary,
      )
    ).get(businessGuid)
      ?: return emptyList()

    return rule.bankAccountFractions.map {
      treasuryCommandConfigMapper.getTreasuryCommand(
        TreasuryCommandConfig.BookTransfer(
          fromAccount = rule.bankAccountGuid?.let { BankAccountSelector.Highbeam(it) }
            ?: BankAccountSelector.Primary,
          toAccount = BankAccountSelector.Highbeam(it.bankAccountGuid),
          amount = AmountSelector.Fractional(
            amountSelector = AmountSelector.Fixed(
              amount = principalAmount,
            ),
            fraction = it.fraction,
          ),
          description = rule.description ?: "Smart yield - Maintain target balance",
        )
      )
    }
  }

  override suspend fun build(rule: RuleConfig, businessGuid: UUID, triggerData: TriggerData) =
    build(
      rule = rule.copy(bankAccountGuid = rule.bankAccountGuid ?: triggerData.bankAccountGuid),
      businessGuid = businessGuid
    )
}
