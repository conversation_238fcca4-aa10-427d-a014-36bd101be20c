package co.highbeam.rulebuilder

import co.highbeam.command.TreasuryCommand
import co.highbeam.command.TreasuryCommandConfigMapper
import co.highbeam.rep.treasury.amount.AmountSelector
import co.highbeam.rep.treasury.bankAccount.BankAccountSelector
import co.highbeam.rep.treasury.command.TreasuryCommandConfig
import co.highbeam.rep.treasury.ruleTrigger.ReceivedPaymentsTriggerData
import co.highbeam.rep.treasury.ruleTrigger.TriggerData
import com.google.inject.Inject
import java.util.UUID
import co.highbeam.rep.treasury.rule.Rule.CapitalBasedFulfillment as RuleConfig

class CapitalBasedFulfillmentBuilder @Inject constructor(
  private val treasuryCommandConfigMapper: TreasuryCommandConfigMapper,
) : RuleBuilder<RuleConfig>() {
  override suspend fun build(rule: RuleConfig, businessGuid: UUID): List<TreasuryCommand> {
    return emptyList()
  }

  override suspend fun build(
    rule: RuleConfig,
    businessGuid: UUID,
    triggerData: TriggerData
  ): List<TreasuryCommand> {
    if (triggerData !is ReceivedPaymentsTriggerData) return emptyList()

    return listOf(
      treasuryCommandConfigMapper.getTreasuryCommand(
        TreasuryCommandConfig.CapitalDrawdown(
          toAccount = BankAccountSelector.Highbeam(triggerData.bankAccountGuid),
          amount = AmountSelector.Deficit(
            threshold = triggerData.amount,
            amountSelector = AmountSelector.BankAccount(
              bankAccount = BankAccountSelector.Highbeam(triggerData.bankAccountGuid)
            ),
          ),
          capitalAccountGuid = rule.capitalAccountGuid,
        )
      )
    )
  }
}
