package co.highbeam.rulebuilder

import co.highbeam.command.TreasuryCommand
import co.highbeam.command.TreasuryCommandConfigMapper
import co.highbeam.money.Balance
import co.highbeam.rep.treasury.amount.AmountSelector
import co.highbeam.rep.treasury.bankAccount.BankAccountSelector
import co.highbeam.rep.treasury.command.TreasuryCommandConfig
import co.highbeam.rep.treasury.ruleTrigger.TriggerData
import com.google.inject.Inject
import java.math.BigDecimal
import java.util.UUID
import co.highbeam.rep.treasury.rule.Rule.CashCardRepayment as RuleConfig

data class CashCardRepaymentBuilder @Inject constructor(
  private val treasuryCommandConfigMapper: TreasuryCommandConfigMapper,
) : RuleBuilder<RuleConfig>() {
  override suspend fun build(rule: RuleConfig, businessGuid: UUID): List<TreasuryCommand> {
    return listOf(
      treasuryCommandConfigMapper.getTreasuryCommand(
        TreasuryCommandConfig.ChargeCardRepayment(
          fromAccount = BankAccountSelector.ChargeCardRepayment(rule.capitalAccountGuid),
          amount = AmountSelector.ChargeCardBalance(
            threshold = Balance.ZERO,
            balanceMultiplier = BigDecimal.ONE,
            capitalAccountGuid = rule.capitalAccountGuid,
          ),
          capitalAccountGuid = rule.capitalAccountGuid,
          createDrawdown = false,
        )
      ),
      treasuryCommandConfigMapper.getTreasuryCommand(
        TreasuryCommandConfig.ChargeCardRepayment(
          fromAccount = BankAccountSelector.HighestBalance,
          amount = AmountSelector.ChargeCardBalance(
            threshold = Balance.ZERO,
            balanceMultiplier = BigDecimal.ONE,
            capitalAccountGuid = rule.capitalAccountGuid,
          ),
          capitalAccountGuid = rule.capitalAccountGuid,
          createDrawdown = false,
        )
      ),
    )
  }

  override suspend fun build(
    rule: RuleConfig,
    businessGuid: UUID,
    triggerData: TriggerData
  ) =
    build(
      rule = rule,
      businessGuid = businessGuid
    )
}
