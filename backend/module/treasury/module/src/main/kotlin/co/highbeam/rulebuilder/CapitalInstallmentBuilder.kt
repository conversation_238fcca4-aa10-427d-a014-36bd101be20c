package co.highbeam.rulebuilder

import co.highbeam.command.TreasuryCommand
import co.highbeam.command.TreasuryCommandConfigMapper
import co.highbeam.rep.treasury.amount.AmountSelector
import co.highbeam.rep.treasury.bankAccount.BankAccountSelector
import co.highbeam.rep.treasury.command.TreasuryCommandConfig
import co.highbeam.rep.treasury.ruleTrigger.TriggerData
import com.google.inject.Inject
import java.util.UUID
import co.highbeam.rep.treasury.rule.Rule.CreditInstallment as RuleConfig

class CapitalInstallmentBuilder @Inject constructor(
  private val treasuryCommandConfigMapper: TreasuryCommandConfigMapper,
) : RuleBuilder<RuleConfig>() {
  override suspend fun build(rule: RuleConfig, businessGuid: UUID): List<TreasuryCommand> {
    return listOf(
      treasuryCommandConfigMapper.getTreasuryCommand(
        TreasuryCommandConfig.CapitalRepayment(
          fromAccount = rule.bankAccountGuid?.let { BankAccountSelector.Highbeam(it) }
            ?: BankAccountSelector.Primary,
          amount = amountSelection(rule),
          capitalAccountGuid = rule.creditAccountGuid,
          pullPartialPayments = rule.pullPartialPayments,
          retryPolicy = mapRetryMode(rule.retryPolicy),
        )
      ),
    )
  }

  private fun mapRetryMode(
    mode: RuleConfig.RetryPolicy
  ): TreasuryCommandConfig.CapitalRepayment.RetryPolicy {
    return when (mode) {
      RuleConfig.RetryPolicy.None ->
        TreasuryCommandConfig.CapitalRepayment.RetryPolicy.None
      RuleConfig.RetryPolicy.PartialAllowed ->
        TreasuryCommandConfig.CapitalRepayment.RetryPolicy.PartialAllowed
      RuleConfig.RetryPolicy.AllOrNothing ->
        TreasuryCommandConfig.CapitalRepayment.RetryPolicy.AllOrNothing
    }
  }

  private fun amountSelection(rule: RuleConfig): AmountSelector =
    when (val amountStrategy = rule.amountStrategy) {
      is RuleConfig.AmountStrategy.MaturityBased ->
        AmountSelector.CapitalMaturityBased(
          netDays = amountStrategy.netDays,
          capitalAccountGuid = rule.creditAccountGuid,
        )
      is RuleConfig.AmountStrategy.LimitMultiplier ->
        AmountSelector.CapitalLimitMultiplier(
          multiplier = amountStrategy.multiplier,
          capitalAccountGuid = rule.creditAccountGuid,
        )

      is RuleConfig.AmountStrategy.UnpaidDrawdown ->
        AmountSelector.CapitalUnpaidDrawdownsMultiplier(
          multiplier = amountStrategy.multiplier,
          capitalAccountGuid = rule.creditAccountGuid,
        )
      is RuleConfig.AmountStrategy.ProRata ->
        AmountSelector.CapitalProRataMultiplier(
          multiplier = amountStrategy.multiplier,
          capitalAccountGuid = rule.creditAccountGuid,
        )
    }

  override suspend fun build(rule: RuleConfig, businessGuid: UUID, triggerData: TriggerData) =
    build(
      rule = rule.copy(bankAccountGuid = rule.bankAccountGuid ?: triggerData.bankAccountGuid),
      businessGuid = businessGuid,
    )
}
