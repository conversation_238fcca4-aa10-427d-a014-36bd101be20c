package co.highbeam.rulebuilder

import co.highbeam.command.TreasuryCommand
import co.highbeam.rep.treasury.rule.Rule
import co.highbeam.rep.treasury.ruleTrigger.TriggerData
import com.google.inject.Inject
import com.google.inject.Injector
import java.util.UUID
import kotlin.reflect.full.callSuspend
import kotlin.reflect.full.memberFunctions

class RuleCommandMapper @Inject constructor(
  private val injector: Injector,
) {
  private val ruleBuilders = RuleBuilder::class.sealedSubclasses.map {
    injector.getInstance(it.java)
  }.associateBy { it.configClass }

  suspend fun getCommand(rule: Rule, businessGuid: UUID): List<TreasuryCommand> {
    val ruleBuilder = ruleBuilders[rule::class] ?: return emptyList()
    val buildMethod = ruleBuilder::class.memberFunctions.first { function ->
      function.name == "build" &&
      function.parameters.size == 3 && // this (receiver) + rule + businessGuid
      function.parameters[1].type.classifier == rule::class &&
      function.parameters[2].type.classifier == UUID::class
    }
    return buildMethod.callSuspend(
      ruleBuilder, rule, businessGuid
    ) as List<TreasuryCommand>
  }

  suspend fun getCommand(
    rule: Rule,
    businessGuid: UUID,
    triggerData: TriggerData,
  ): List<TreasuryCommand> {
    val ruleBuilder = ruleBuilders[rule::class] ?: return emptyList()
    val buildMethod = ruleBuilder::class.memberFunctions.first { function ->
      function.name == "build" &&
      function.parameters.size == 4 && // this (receiver) + rule + businessGuid + triggerData
      function.parameters[1].type.classifier == rule::class &&
      function.parameters[2].type.classifier == UUID::class &&
      function.parameters[3].type.classifier == TriggerData::class
    }
    return buildMethod.callSuspend(
      ruleBuilder, rule, businessGuid, triggerData
    ) as List<TreasuryCommand>
  }
}
