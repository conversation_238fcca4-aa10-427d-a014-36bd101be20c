package co.highbeam.rulebuilder

import co.highbeam.command.TreasuryCommand
import co.highbeam.command.TreasuryCommandConfigMapper
import co.highbeam.rep.treasury.amount.AmountSelector
import co.highbeam.rep.treasury.bankAccount.BankAccountSelector
import co.highbeam.rep.treasury.command.TreasuryCommandConfig
import co.highbeam.rep.treasury.ruleTrigger.TriggerData
import com.google.inject.Inject
import java.util.UUID
import co.highbeam.rep.treasury.rule.Rule.CapitalInstallmentRetry as RuleConfig

class CapitalInstallmentSweepBuilder @Inject constructor(
  private val treasuryCommandConfigMapper: TreasuryCommandConfigMapper,
) : RuleBuilder<RuleConfig>() {
  override suspend fun build(rule: RuleConfig, businessGuid: UUID): List<TreasuryCommand> {
    return listOf(
      treasuryCommandConfigMapper.getTreasuryCommand(
        TreasuryCommandConfig.CapitalRepaymentSweep(
          fromAccount = rule.bankAccountGuid?.let { BankAccountSelector.Highbeam(it) }
            ?: BankAccountSelector.Primary,
          capitalAccountGuid = rule.capitalAccountGuid,
          amount = AmountSelector.Minimum(
            listOf(
              AmountSelector.Fixed(rule.amount),
              AmountSelector.CapitalOwedBalance(rule.capitalAccountGuid)
            )
          ),
          pullPartialPayments = rule.pullPartialPayments,
        )
      )
    )
  }

  override suspend fun build(
    rule: RuleConfig,
    businessGuid: UUID,
    triggerData: TriggerData,
  ): List<TreasuryCommand> =
    build(
      rule = rule.copy(bankAccountGuid = rule.bankAccountGuid ?: triggerData.bankAccountGuid),
      businessGuid = businessGuid
    )
}
