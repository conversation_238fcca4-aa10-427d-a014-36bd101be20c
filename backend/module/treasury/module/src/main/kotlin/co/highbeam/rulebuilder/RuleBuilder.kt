package co.highbeam.rulebuilder

import co.highbeam.command.TreasuryCommand
import co.highbeam.rep.treasury.rule.Rule
import co.highbeam.rep.treasury.ruleTrigger.TriggerData
import io.leangen.geantyref.GenericTypeReflector
import java.util.UUID
import kotlin.reflect.KClass

sealed class RuleBuilder<T : Rule> {
  @Suppress("UNCHECKED_CAST")
  val configClass: KClass<T> = (
    GenericTypeReflector.getTypeParameter(
      this::class.java,
      RuleBuilder::class.java.typeParameters[0]
    ) as Class<T>
    ).kotlin

  abstract suspend fun build(rule: T, businessGuid: UUID): List<TreasuryCommand>

  abstract suspend fun build(
    rule: T,
    businessGuid: UUID,
    triggerData: TriggerData
  ): List<TreasuryCommand>
}
